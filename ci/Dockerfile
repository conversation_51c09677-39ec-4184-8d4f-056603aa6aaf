# -------- Builder Stage --------- #
FROM alpine:latest AS builder

RUN apk update && apk upgrade && apk add --update nodejs npm

WORKDIR /usr/app

ARG NPM_TOKEN
ENV NPM_TOKEN=$NPM_TOKEN

COPY . .

RUN npm ci
RUN npm run build

# -------- Runner Stage --------- #

FROM alpine:latest AS runner

LABEL maintainer="<EMAIL>"

RUN apk update && apk upgrade && apk add --update nodejs npm

RUN npm i -g pm2

COPY --from=builder /usr/app/dist /usr/app/dist
COPY --from=builder /usr/app/node_modules /usr/app/dist/node_modules
COPY --from=builder /usr/app/heron.js /usr/app/dist/heron.js
COPY --from=builder /usr/app/ci/pm2/ecosystem.config.js /usr/app/dist/ecosystem.config.js

WORKDIR /usr/app/dist

ARG SERVICE_VERSION
ENV SERVICE_VERSION=$SERVICE_VERSION

EXPOSE 3000

CMD ["sh", "-c", "pm2-runtime start ecosystem.config.js"]