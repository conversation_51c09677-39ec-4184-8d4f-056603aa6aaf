stages:
    # - test
    - build
    - deploy

# test:
#     stage: test
#     only:
#         - tags
#     before_script:
#         - apk add --no-cache nodejs npm
#     script:
#         - npm install
#         - echo "$TEST_ENVIRONMENT" > .env.test
#         - npm run test:integration

build:
    stage: build
    only:
        - tags
    when: on_success
    script:
        - docker build -t $REGISTRY_SERVER/$REGISTRY_TAG:$CI_COMMIT_TAG --build-arg NPM_TOKEN=${NPM_TOKEN} --build-arg SERVICE_VERSION=${CI_COMMIT_TAG} -f ci/Dockerfile .
        - echo "$REGISTRY_PASSWORD" | docker login $REGISTRY_SERVER -u $REGISTRY_USER --password-stdin
        - docker push $REGISTRY_SERVER/$REGISTRY_TAG:$CI_COMMIT_TAG
    # dependencies:
        # - test

deploy:
    stage: deploy
    tags:
        - mcp-runner
    only:
        - tags
    when: on_success
    before_script:
        - apk add --no-cache curl jq
        - |
            if [[ "$CI_COMMIT_TAG" == *"-alpha"* ]] || [[ "$CI_COMMIT_TAG" == *"-rc"* ]]; then
              export ENV_NAME="STAGING"
            else
              export ENV_NAME="PRODUCTION"
            fi
            echo "Deploying to $ENV_NAME environment"
    script:
        - |
            # Trigger Jenkins job and capture the response
            RESPONSE=$(curl -s -X POST "$JENKINS_JOB_URL/buildWithParameters" \
              --user "$JENKINS_USER:$JENKINS_API_TOKEN" \
              --data-urlencode "ServiceName=$JENKINS_SERVICE_NAME" \
              --data-urlencode "AppVersion=$CI_COMMIT_TAG" \
              --data-urlencode "Namespace=$([ "$ENV_NAME" = "PRODUCTION" ] && echo "default" || echo "fake-zone")" \
              --data-urlencode "Choices=Deploy" \
              --data-urlencode "ReleaseNote=$CI_COMMIT_MESSAGE")

            echo "Jenkins response: $RESPONSE"

            echo "Jenkins job triggered successfully"

            # Wait a moment for the job to be created
            sleep 10

            # Get the latest build number
            BUILD_NUMBER=$(curl -s --user "$JENKINS_USER:$JENKINS_API_TOKEN" "$JENKINS_JOB_URL/api/json" | jq -r '.lastBuild.number')

            echo "Jenkins build number: $BUILD_NUMBER"

            # Monitor job status
            echo "Monitoring Jenkins deployment job..."
            echo "Monitoring build #$BUILD_NUMBER"

            while true; do
              BUILD_STATUS=$(curl -s --user "$JENKINS_USER:$JENKINS_API_TOKEN" "$JENKINS_JOB_URL/$BUILD_NUMBER/api/json" | jq -r '.result')
              BUILD_BUILDING=$(curl -s --user "$JENKINS_USER:$JENKINS_API_TOKEN" "$JENKINS_JOB_URL/$BUILD_NUMBER/api/json" | jq -r '.building')
              
              echo "Build status: $BUILD_STATUS, Building: $BUILD_BUILDING"
              
              if [ "$BUILD_BUILDING" = "false" ]; then
                echo "Build completed with status: $BUILD_STATUS"
                break
              fi
              
              echo "Build still in progress, waiting 30 seconds..."
              sleep 30
            done

            # Determine status emoji and message
            if [ "$BUILD_STATUS" = "SUCCESS" ]; then
              STATUS_EMOJI="✅"
              STATUS_MESSAGE="Successfully Deployed"
            elif [ "$BUILD_STATUS" = "FAILURE" ]; then
              STATUS_EMOJI="❌"
              STATUS_MESSAGE="Deployment Failed"
            else
              STATUS_EMOJI="⚠️"
              STATUS_MESSAGE="Deployment Status: $BUILD_STATUS"
            fi

            # Send notification
            curl --location $GOOGLE_CHAT_WEBHOOK_URL \
            --header 'Content-Type: application/json' \
            --data "{
              \"cards\": [
                {
                  \"header\": {
                    \"title\": \"$STATUS_EMOJI $STATUS_MESSAGE\",
                    \"subtitle\": \"$ENV_NAME · $JENKINS_SERVICE_NAME · $CI_COMMIT_TAG\"
                  },
                  \"sections\": [
                    {
                      \"widgets\": [
                        {
                          \"keyValue\": {
                            \"topLabel\": \"🔧 Service\",
                            \"content\": \"$JENKINS_SERVICE_NAME\"
                          }
                        },
                        {
                          \"keyValue\": {
                            \"topLabel\": \"🏷 Version\",
                            \"content\": \"$CI_COMMIT_TAG\"
                          }
                        },
                        {
                          \"keyValue\": {
                            \"topLabel\": \"🌍 Environment\",
                            \"content\": \"$ENV_NAME\"
                          }
                        },
                        {
                          \"keyValue\": {
                            \"topLabel\": \"📊 Status\",
                            \"content\": \"$BUILD_STATUS\"
                          }
                        }
                      ]
                    },
                    {
                      \"header\": \"📄 Release Notes\",
                      \"widgets\": [
                        {
                          \"textParagraph\": {
                            \"text\": \"$CI_COMMIT_MESSAGE\"
                          }
                        }
                      ]
                    }
                  ]
                }
              ]
            }"
    dependencies:
        - build
