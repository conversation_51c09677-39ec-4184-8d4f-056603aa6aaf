{"name": "@cbidigital/mcp-service", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node dist/main.js", "dev": "npx tsx watch src/main.ts", "build": "node build.js && tsc --build tsconfig.json && tsc-alias", "nodemon": "nodemon --config nodemon.debug.json", "test": "echo \"Error: no test specified\" && exit 1", "migrate:latest": "npx knex migrate:latest --env production", "format": "prettier --write 'src/**/*.{js,ts}'", "lint": "eslint . --ext .js,.ts,.jsx,.tsx"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"@cbidigital/aqua-ddd": "^1.0.0-rc.9", "@heronjs/common": "3.4.10", "@heronjs/core": "3.5.10", "@heronjs/express": "3.1.16", "@modelcontextprotocol/sdk": "^1.13.1", "axios": "^1.10.0", "class-validator": "^0.14.2", "http-status-codes": "^2.3.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "zod": "^3.25.67"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.4", "eslint": "^9.29.0", "eslint-plugin-prettier": "^5.5.0", "fs-extra": "^11.3.0", "globals": "^16.2.0", "prettier": "^3.6.0", "ts-node": "^10.9.2", "tsc-alias": "^1.8.16", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.35.0"}}