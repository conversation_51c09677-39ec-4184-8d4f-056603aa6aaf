import "reflect-metadata";
import { HeronJS } from "@heronjs/core";
import { McpServer } from "@features/mcp/infra";
import { <PERSON><PERSON>eeper, Module } from "@heronjs/common";
import { AuthContext } from "./context/auth.context";
import { NewTool } from "@features/mcp/infra/mcp/tools";
import { McpRest } from "@features/mcp/presentation/rest";
import { GlobalApiErrorInterceptor } from "./interceptors";
import { HandleMcpRequestUseCase, RegistryService } from "@features/mcp";
import { AnalyzeCodePrompt, UserProfileResource } from "@features/mcp/infra/mcp";
import { AddTwoNumbersTool } from "@features/mcp/infra/mcp/tools/add-two-numbers.tool";

@Module({
  services: [RegistryService],
  controllers: [McpRest],
  providers: [
    // usecases
    HandleMcpRequestUseCase,

    // infra
    McpServer,

    // tools
    AddTwoNumbersTool,
    NewTool,

    // resources
    UserProfileResource,

    // prompts
    AnalyzeCodePrompt,
  ],
})
@GateKeeper(AuthContext, AuthContext.Resolver)
export class AppModule {}

const main = async () => {
  const app = await HeronJS.create({ module: AppModule });
  await app.listen({
    port: 3000,
    options: {
      cors: {
        origin: "*",
        preflightContinue: false,
        methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
      },
      globalError: GlobalApiErrorInterceptor,
    },
  });
};

(async () => main())();
