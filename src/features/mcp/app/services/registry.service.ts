import { MCP_SVC_INJECT_TOKENS } from "@features/mcp/shared";
import { IMcpPrompt, IMcpResource, IMcpServer, IMcpTool } from "@features/mcp/domain";
import { ILogger, Inject, Logger, OnComplete, OnStart, Service } from "@heronjs/common";

@Service({ mode: "Initialize" })
export class RegistryService {
  private readonly logger: ILogger;

  constructor(
    @Inject(MCP_SVC_INJECT_TOKENS.INFRA.MCP_SERVER) private readonly mcpServer: IMcpServer,
  ) {
    this.logger = new Logger(RegistryService.name);
  }

  @OnStart()
  onStart() {
    this.logger.info("RegistryService started.");

    // Register tools
    for (const token of Object.values(MCP_SVC_INJECT_TOKENS.MCP_TOOLS)) {
      const tool: IMcpTool = use("Container").resolve(token);
      this.mcpServer.registerTool(tool);
    }

    // Register resources
    for (const token of Object.values(MCP_SVC_INJECT_TOKENS.MCP_RESOURCES)) {
      const resource: IMcpResource = use("Container").resolve(token);
      this.mcpServer.registerResource(resource);
    }

    // Register prompts
    for (const token of Object.values(MCP_SVC_INJECT_TOKENS.MCP_PROMPTS)) {
      const prompt: IMcpPrompt = use("Container").resolve(token);
      this.mcpServer.registerPrompt(prompt);
    }
  }

  @OnComplete()
  onComplete() {
    this.logger.info("RegistryService completed.");
  }
}
