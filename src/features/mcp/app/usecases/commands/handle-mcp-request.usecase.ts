import { z } from "zod";
import { IMcpServer } from "@features/mcp/domain";
import { MCP_SVC_INJECT_TOKENS } from "@features/mcp/shared";
import { HttpRequest, HttpResponse } from "@heronjs/express";
import { IUseCase, UseCase, UseCaseContext } from "@cbidigital/aqua-ddd";
import { ILogger, Inject, Lifecycle, Logger, Provider } from "@heronjs/common";
import { StreamableHTTPServerTransport } from "@modelcontextprotocol/sdk/server/streamableHttp.js";

export type HandleMcpRequestUseCaseInput = {
  req: HttpRequest;
  res: HttpResponse;
};

export type HandleMcpRequestUseCaseOutput = { status: boolean };

const HandleMcpRequestUseCaseInputSchema = z.object({});

export type IHandleMcpRequestUseCase = IUseCase<
  HandleMcpRequestUseCaseInput,
  HandleMcpRequestUseCaseOutput,
  UseCaseContext
>;

@Provider({
  token: MCP_SVC_INJECT_TOKENS.USECASE.HANDLE_MCP_REQUEST,
  scope: Lifecycle.Transient,
})
export class HandleMcpRequestUseCase
  extends UseCase<HandleMcpRequestUseCaseInput, HandleMcpRequestUseCaseOutput, UseCaseContext>
  implements IHandleMcpRequestUseCase
{
  private readonly logger: ILogger;
  constructor(
    @Inject(MCP_SVC_INJECT_TOKENS.INFRA.MCP_SERVER) private readonly mcpServer: IMcpServer,
  ) {
    super();
    this.setMethods(this.processing);
    this.logger = new Logger(this.constructor.name);
  }

  processing = async (input: HandleMcpRequestUseCaseInput) => {
    const { req, res } = input;
    const server = this.mcpServer.getServer();
    const transport: StreamableHTTPServerTransport = new StreamableHTTPServerTransport({
      sessionIdGenerator: undefined,
    });

    await server.connect(transport);
    await transport.handleRequest(req, res, req.body);
    res.on("close", () => {
      console.log("Request closed");
      transport.close();
      //   server.close();
    });
  };
}
