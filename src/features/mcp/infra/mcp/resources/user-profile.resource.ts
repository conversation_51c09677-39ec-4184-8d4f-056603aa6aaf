import { <PERSON><PERSON><PERSON>, Logger, Provider } from "@heronjs/common";
import { MCP_SVC_INJECT_TOKENS } from "@features/mcp/shared";
import { ReadResourceResult } from "@modelcontextprotocol/sdk/types";
import { IMcpResource, BaseMcpResource, McpResourceCallback } from "@features/mcp/domain";

@Provider({ token: MCP_SVC_INJECT_TOKENS.MCP_RESOURCES.USER_PROFILE })
export class UserProfileResource extends BaseMcpResource implements IMcpResource {
  private readonly logger: ILogger;

  constructor() {
    super({
      name: "user-profile",
      uriOrTemplate: "https://jsonplaceholder.typicode.com/posts",
      config: {
        title: "Application Config",
        description: "Application configuration data",
        mimeType: "text/plain",
      },
    });
    this.logger = new Logger(this.constructor.name);
    this.logger.info("UserProfileResource initialized successfully.");
  }

  callback: McpResourceCallback = async (uri: URL) => {
    const data = await fetch(uri.href, {
      method: "GET",
    });
    const json = await data.json();
    const text = JSON.stringify(json);

    const result: ReadResourceResult = {
      contents: [
        {
          uri: uri.href,
          mimeType: "text/plain",
          text: text,
        },
      ],
    };
    return result;
  };
}
