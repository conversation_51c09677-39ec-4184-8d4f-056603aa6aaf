import { z } from "zod";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Provider } from "@heronjs/common";
import { MCP_SVC_INJECT_TOKENS } from "@features/mcp/shared";
import { PromptCallback } from "@modelcontextprotocol/sdk/server/mcp";
import { RequestHandlerExtra } from "@modelcontextprotocol/sdk/shared/protocol";
import { IMcpPrompt, BaseMcpPrompt, McpPromptArgsSchema } from "@features/mcp/domain";
import {
  ServerRequest,
  GetPromptResult,
  ServerNotification,
} from "@modelcontextprotocol/sdk/types";

@Provider({ token: MCP_SVC_INJECT_TOKENS.MCP_PROMPTS.ANALYZE_CODE })
export class AnalyzeCodePrompt extends BaseMcpPrompt implements IMcpPrompt {
  private readonly logger: ILogger;

  constructor() {
    super({
      name: "analyze-code",
      title: "Analyze Code",
      description: "Use this prompt to analyze code.",
      argsSchema: {
        code: z.string().describe("The code to analyze"),
      },
    });
    this.logger = new Logger(this.constructor.name);
    this.logger.info("AnalyzeCodePrompt initialized successfully.");
  }

  callback: PromptCallback<McpPromptArgsSchema> = async (
    args: { [x: string]: string | undefined },
    _extra: RequestHandlerExtra<ServerRequest, ServerNotification>, // unused but required by interface
  ): Promise<GetPromptResult> => {
    this.logger.info("AnalyzeCodePrompt callback called", { args });

    const code = args.code || "";
    const result: GetPromptResult = {
      messages: [
        {
          role: "user",
          content: {
            type: "text",
            text: `Please analyze the following code:\n\n${code}`,
          },
        },
      ],
    };
    return result;
  };
}
