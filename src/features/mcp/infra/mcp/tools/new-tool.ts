import { z } from "zod";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Provider } from "@heronjs/common";
import { MCP_SVC_INJECT_TOKENS } from "@features/mcp/shared";
import { BaseMcpTool, IMcpTool, McpToolCallbackOutput } from "@features/mcp/domain";

export type NewToolInput = { a: number; b: number };

@Provider({ token: MCP_SVC_INJECT_TOKENS.MCP_TOOLS.NEW_TOOL })
export class NewTool extends BaseMcpTool implements IMcpTool {
  private readonly logger: ILogger;

  constructor() {
    super({
      name: "new-tool",
      title: "New Tool",
      description: "Use this tool to do something.",
      inputSchema: {
        a: z.number().describe("The first number to add"),
        b: z.number().describe("The second number to add"),
      },
    });
    this.logger = new Logger(this.constructor.name);
    this.logger.info("NewTool initialized successfully.");
  }

  async callback(input: NewToolInput): Promise<McpToolCallbackOutput> {
    return Promise.resolve({
      content: [
        {
          type: "text",
          text: `New tool output: ${input.a + input.b}`,
        },
      ],
    });
  }
}
