import { z } from "zod";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Provider } from "@heronjs/common";
import { MCP_SVC_INJECT_TOKENS } from "@features/mcp/shared";
import { BaseMcpTool, IMcpTool, McpToolCallbackOutput } from "@features/mcp/domain";

export type AddTwoNumbersToolInput = {
  a: number;
  b: number;
};

@Provider({ token: MCP_SVC_INJECT_TOKENS.MCP_TOOLS.ADD_TWO_NUMBERS_TOOL })
export class AddTwoNumbersTool extends BaseMcpTool implements IMcpTool {
  private readonly logger: ILogger;

  constructor() {
    super({
      name: "add-two-numbers",
      title: "Add Two Numbers",
      description: "Use this tool to add two numbers together.",
      inputSchema: {
        a: z.number().describe("The first number to add"),
        b: z.number().describe("The second number to add"),
      },
    });
    this.logger = new Logger(this.constructor.name);
    this.logger.info("AddTwoNumbersTool initialized successfully.");
  }

  async callback(input: AddTwoNumbersToolInput): Promise<McpToolCallbackOutput> {
    return Promise.resolve({
      content: [
        {
          type: "text",
          text: `${input.a + input.b}`,
        },
      ],
    });
  }
}
