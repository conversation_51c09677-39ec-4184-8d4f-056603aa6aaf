import { HttpRequest, HttpResponse } from "@heronjs/express";
import { IHandleMcpRequestUseCase } from "@features/mcp/app";
import { MCP_SVC_INJECT_TOKENS } from "@features/mcp/shared";
import { Rest, Post, Request, Response, Fuse } from "@heronjs/common";

@Rest("/mcp")
export class McpRest {
  @Post({ uri: "/" })
  async handle(
    @Fuse(MCP_SVC_INJECT_TOKENS.USECASE.HANDLE_MCP_REQUEST)
    handleMcpRequestUseCase: IHandleMcpRequestUseCase,
    @Request() req: HttpRequest,
    @Response() res: HttpResponse,
  ) {
    try {
      await handleMcpRequestUseCase.exec({ req, res });
    } catch (error) {
      console.error("Error handling MCP request:", error);
      if (!res.headersSent) {
        res.status(500).json({
          jsonrpc: "2.0",
          error: {
            code: -32603,
            message: "Internal server error",
          },
          id: null,
        });
      }
    }
  }
}
