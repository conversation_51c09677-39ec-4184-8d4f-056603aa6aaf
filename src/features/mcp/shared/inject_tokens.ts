export const MCP_SVC_INJECT_TOKENS = {
  USECASE: {
    HANDLE_MCP_REQUEST: Symbol("HANDLE_MCP_REQUEST").toString(),
  },

  INFRA: {
    MCP_SERVER: Symbol("MCP_SERVER").toString(),
  },

  MCP_TOOLS: {
    ADD_TWO_NUMBERS_TOOL: Symbol("ADD_TWO_NUMBERS_TOOL").toString(),
    NEW_TOOL: Symbol("NEW_TOOL").toString(),
  },

  MCP_RESOURCES: {
    USER_PROFILE: Symbol("USER_PROFILE").toString(),
  },

  MCP_PROMPTS: {
    ANALYZE_CODE: Symbol("ANALYZE_CODE").toString(),
  },
};
