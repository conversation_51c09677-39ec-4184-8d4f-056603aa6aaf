import "reflect-metadata";

const columnMetadataKey = Symbol("column");

export function Column(name?: string): PropertyDecorator {
  return (target: object, propertyKey: string | symbol) => {
    const columnName =
      name ?? propertyKey.toString().replace(/[A-Z]/g, (match) => `_${match.toLowerCase()}`);

    Reflect.defineMetadata(columnMetadataKey, columnName, target, propertyKey);
  };
}

export function getColumnName(target: any, propertyKey: string) {
  const metadata = Reflect.getMetadata(columnMetadataKey, target, propertyKey);

  return metadata as string;
}
