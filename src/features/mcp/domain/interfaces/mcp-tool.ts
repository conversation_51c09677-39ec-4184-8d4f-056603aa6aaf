import { z } from "zod";
import { Optional } from "@heronjs/common";
import { ToolAnnotations } from "@modelcontextprotocol/sdk/types";

export type McpToolCallbackInput = Record<string, any>;
export type McpToolCallbackOutput = {
  content: {
    type: "text";
    text: string;
  }[];
};

export interface IMcpTool {
  name: string;
  title: Optional<string>;
  description: Optional<string>;
  inputSchema: Optional<z.ZodRawShape>;
  outputSchema: Optional<z.ZodRawShape>;
  annotations: Optional<ToolAnnotations>;
  callback(input: McpToolCallbackInput): Promise<McpToolCallbackOutput>;
}

export abstract class BaseMcpTool implements IMcpTool {
  private readonly _name: string;
  private readonly _title: Optional<string>;
  private readonly _description: Optional<string>;
  private readonly _inputSchema: Optional<z.ZodRawShape>;
  private readonly _outputSchema: Optional<z.ZodRawShape>;
  private readonly _annotations: Optional<ToolAnnotations>;

  constructor({
    name,
    title,
    description,
    inputSchema,
    outputSchema,
    annotations,
  }: {
    name: string;
    title?: Optional<string>;
    description?: Optional<string>;
    inputSchema?: Optional<z.ZodRawShape>;
    outputSchema?: Optional<z.ZodRawShape>;
    annotations?: Optional<ToolAnnotations>;
  }) {
    this._name = name;
    this._title = title;
    this._description = description;
    this._inputSchema = inputSchema;
    this._outputSchema = outputSchema;
    this._annotations = annotations;
  }

  get name(): string {
    return this._name;
  }

  get title(): Optional<string> {
    return this._title;
  }

  get inputSchema(): Optional<z.ZodRawShape> {
    return this._inputSchema;
  }

  get outputSchema(): Optional<z.ZodRawShape> {
    return this._outputSchema;
  }

  get description(): Optional<string> {
    return this._description;
  }

  get annotations(): Optional<ToolAnnotations> {
    return this._annotations;
  }

  abstract callback(input: McpToolCallbackInput): Promise<McpToolCallbackOutput>;
}
