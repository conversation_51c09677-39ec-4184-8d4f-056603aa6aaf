import { IMcpTool } from "@features/mcp/domain/interfaces/mcp-tool";
import { IMcpPrompt } from "@features/mcp/domain/interfaces/mcp-prompt";
import { IMcpResource } from "@features/mcp/domain/interfaces/mcp-resource";

export interface IMcpServer {
  getServer(): {
    connect: (transport: any) => Promise<void>;
  };
  registerTool(tool: IMcpTool): void;
  registerPrompt(prompt: IMcpPrompt): void;
  registerResource(resource: IMcpResource): void;
}
