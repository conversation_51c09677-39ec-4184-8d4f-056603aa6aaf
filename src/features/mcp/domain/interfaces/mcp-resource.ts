import {
  ResourceMetadata,
  ResourceTemplate,
  ReadResourceCallback,
  ReadResourceTemplateCallback,
} from "@modelcontextprotocol/sdk/server/mcp";

export type McpResourceConfig = ResourceMetadata;
export type McpResourceTemplate = ResourceTemplate;
export type McpResourceCallback = ReadResourceCallback | ReadResourceTemplateCallback;
export type McpResourceConstructorPayload = {
  name: string;
  uriOrTemplate: string | McpResourceTemplate;
  config: McpResourceConfig;
};

export interface IMcpResource {
  name: string;
  uriOrTemplate: string | McpResourceTemplate;
  config: McpResourceConfig;
  callback: McpResourceCallback;
}

export abstract class BaseMcpResource implements IMcpResource {
  private readonly _name: string;
  private readonly _uriOrTemplate: string | McpResourceTemplate;
  private readonly _config: McpResourceConfig;

  constructor({ name, uriOrTemplate, config }: McpResourceConstructorPayload) {
    this._name = name;
    this._uriOrTemplate = uriOrTemplate;
    this._config = config;
  }

  get name(): string {
    return this._name;
  }

  get uriOrTemplate(): string | McpResourceTemplate {
    return this._uriOrTemplate;
  }

  get config(): McpResourceConfig {
    return this._config;
  }

  abstract callback: McpResourceCallback;
}
