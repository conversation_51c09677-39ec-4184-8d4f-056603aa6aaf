import { z } from "zod";
import { Optional } from "@heronjs/common";
import { PromptCallback } from "@modelcontextprotocol/sdk/server/mcp";

export type McpPromptArgsSchema = {
  [k: string]:
    | z.ZodType<string, z.ZodTypeDef, string>
    | z.ZodOptional<z.ZodType<string, z.ZodTypeDef, string>>;
};

export type McpPromptConstructorPayload = {
  name: string;
  title?: Optional<string>;
  description?: Optional<string>;
  argsSchema?: Optional<McpPromptArgsSchema>;
};

export interface IMcpPrompt {
  name: string;
  title: Optional<string>;
  description: Optional<string>;
  argsSchema: Optional<McpPromptArgsSchema>;
  callback: PromptCallback<McpPromptArgsSchema>;
}

export abstract class BaseMcpPrompt implements IMcpPrompt {
  private readonly _name: string;
  private readonly _title: Optional<string>;
  private readonly _description: Optional<string>;
  private readonly _argsSchema: Optional<McpPromptArgsSchema>;

  constructor({ name, title, description, argsSchema }: McpPromptConstructorPayload) {
    this._name = name;
    this._title = title;
    this._description = description;
    this._argsSchema = argsSchema;
  }

  get name(): string {
    return this._name;
  }

  get title(): Optional<string> {
    return this._title;
  }

  get description(): Optional<string> {
    return this._description;
  }

  get argsSchema(): Optional<McpPromptArgsSchema> {
    return this._argsSchema;
  }

  abstract callback: PromptCallback<McpPromptArgsSchema>;
}
