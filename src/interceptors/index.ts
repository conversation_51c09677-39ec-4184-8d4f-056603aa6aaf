import { APIError, HttpResponseUtils, isString, RuntimeError } from "@heronjs/common";
import {
  type ExpressErrorInterceptor,
  type ExpressInterceptor,
  type Next,
  type HttpRequest as Request,
  type HttpResponse as Response,
} from "@heronjs/express";
import { StatusCodes } from "http-status-codes";
import { ZodError } from "zod";

export const handelGlobalApiError = (err: Error, res: Response) => {
  if (err instanceof APIError) {
    const cin = isString(err.code) ? parseInt(err.code) : err.code;
    return res.status(cin).send(HttpResponseUtils.error(err));
  } else if (err instanceof ZodError) {
    return res.status(StatusCodes.BAD_REQUEST).send({
      error: "Validation Error",
      message: err.issues.map((issue) => issue.message).join(", "),
      issues: err.issues,
    });
  } else if (err instanceof RuntimeError) {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).send(HttpResponseUtils.error(err));
  } else {
    return res.status(StatusCodes.INTERNAL_SERVER_ERROR).send({
      error: err.name,
      message: err.message,
    });
  }
};

export const GlobalApiErrorInterceptor: ExpressErrorInterceptor = (
  err: Error,
  req: Request,
  res: Response,
  next: Next,
) => {
  if (err) return handelGlobalApiError(err, res);
  return next();
};

export const AuthInternalAPIInterceptor: ExpressInterceptor = (
  req: Request,
  res: Response,
  next: Next,
) => {
  const internalApiKey = req.headers["internal-api-key"];
  return next();
};
