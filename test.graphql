"""
Directive for documentation categorization.
Used to organize types and fields into logical groups in the generated documentation.
"""
directive @doc(
  category: String!
) on SCALAR | FIELD_DEFINITION | ENUM | ENUM_VALUE | INPUT_OBJECT | INTERFACE | UNION | OBJECT

"""
Custom directive for validating enum values on input fields.
Restricts input values to a predefined set of allowed strings.
"""
directive @enum(values: [String!]!) on ARGUMENT_DEFINITION | INPUT_FIELD_DEFINITION

"""
Category information associated with product attributes.
Used to group and classify attribute-based analytics.
"""
type AttributeCategory {
  """
  Category name.
  """
  name: String!

  """
  Unique hash key identifier for the category.
  """
  h_key: String!
}

"""
Product attribute analytics entity containing sales, inventory, and performance metrics.
Provides detailed analytics for specific product attributes (like color, size, etc.)
with comprehensive business intelligence data.
"""
type Attribute {
  """
  The specific attribute value being analyzed (e.g., 'Red', 'Large', 'Cotton').
  """
  attribute_value: String

  """
  Product identifier from the source system.
  """
  product_id: String

  """
  Categories associated with this attribute.
  """
  categories: [AttributeCategory]

  """
  Total gross sales revenue before returns and discounts.
  """
  gross_sales: Float

  """
  Total quantity sold (gross, before returns).
  """
  gross_qty: Int

  """
  Total value of returned items.
  """
  return_value: Float

  """
  Total quantity of returned items.
  """
  return_qty: Int

  """
  Return rate as a percentage (return_qty / gross_qty * 100).
  """
  return_rate: Float

  """
  Net sales after returns (gross_sales - return_value).
  """
  net_sales: Float

  """
  Net quantity after returns (gross_qty - return_qty).
  """
  net_qty: Int

  """
  Total sales including taxes, shipping, etc.
  """
  total_sales: Float

  """
  Average sales per day over the analysis period.
  """
  sales_per_day: Float

  """
  Estimated days of sales remaining based on available-to-sell inventory.
  """
  sales_day_left_ats: Float

  """
  Sell-through rate as a percentage.
  """
  sell_through: Float

  """
  Total tax amount collected.
  """
  tax: Float

  """
  Total discount amount applied.
  """
  discount: Float

  """
  Total shipping charges.
  """
  shipping: Float

  """
  Number of product page views.
  """
  product_views: Int

  """
  Current stock quantity on hand.
  """
  stock: Int

  """
  Quantity on order from suppliers.
  """
  on_order: Int

  """
  Available-to-sell quantity (stock - committed).
  """
  ats: Int

  """
  Total inventory value at cost.
  """
  total_inventory_value: Float

  """
  Current selling price.
  """
  price: Float

  """
  Product name.
  """
  name: String

  """
  Product image URL.
  """
  image: String

  """
  Reorder point - minimum stock level before reordering.
  """
  rop: Float

  """
  Conversion rate (purchases / views).
  """
  conversion_rate: Float

  """
  Percentage of total gross sales this attribute represents.
  """
  gross_sales_percentage: Float

  """
  Forecasted sales value.
  """
  forecast_value: Float

  """
  Recommended reorder quantity.
  """
  re_order_qty: Int

  """
  Gross profit (net_sales - cost).
  """
  gross_profit: Float

  """
  Gross margin percentage.
  """
  gross_margin: Float

  """
  Weeks of supply remaining.
  """
  wos: Float

  """
  Percentage of total stock this attribute represents.
  """
  stock_percentage: Float

  """
  Forecasted sales per day.
  """
  forecast_sales_per_day: Float
}

"""
Input parameters for querying product attribute analytics.
Requires a specific product and attribute type to analyze.
"""
input AttributeQueryInput {
  """
  Hash key of the product to analyze.
  """
  product_h_key: String!

  """
  Attribute type to analyze (e.g., 'color', 'size', 'material').
  """
  attribute: String!

  """
  Start date for the analysis period (ISO 8601 format).
  """
  from_date: String

  """
  End date for the analysis period (ISO 8601 format).
  """
  to_date: String

  """
  Start date for forecast analysis (ISO 8601 format).
  """
  forecast_from_date: String

  """
  End date for forecast analysis (ISO 8601 format).
  """
  forecast_to_date: String

  """
  Sort criteria for the results.
  """
  sort: [SortInput]
}

type Query {
  """
  Query product attribute analytics for a specific product and attribute type.
  Returns detailed sales, inventory, and performance metrics for each attribute value.

  Example:
  ```graphql
  query {
    attributes(query: {
      product_h_key: "prod_123",
      attribute: "color",
      from_date: "2024-01-01",
      to_date: "2024-12-31",
      sort: [{ field: "gross_sales", order: "desc" }]
    }) {
      attribute_value
      gross_sales
      gross_qty
      return_rate
      stock
      ats
      conversion_rate
    }
  }
  ```
  """
  attributes(query: AttributeQueryInput!): [Attribute]

  """
  Query categories with pagination, sorting, and filtering capabilities.

  Example:
  ```graphql
  query {
    categories(query: {
      limit: 20,
      sort: [{ field: "name", order: "asc" }],
      filter: { name: { icontains: "electronics" } },
      semantic_search: {
        text: "electronics",
        min_score: 0.5
      }
    }) {
      total_count
      items {
        h_key
        name
        from_source
      }
    }
  }
  ```
  """
  categories(query: CategoriesQueryInput!): CategoriesQueryOutput!

  """
  Query collections with pagination, sorting, and filtering capabilities.

  Example:
  ```graphql
  query {
    collections(query: {
      limit: 20,
      sort: [{ field: "name", order: "asc" }],
      filter: { name: { icontains: "summer" } },
      semantic_search: {
        text: "summer",
        min_score: 0.5
      }
    }) {
      total_count
      items {
        h_key
        name
        from_source
      }
    }
  }
  ```
  """
  collections(query: CollectionsQueryInput!): CollectionsQueryOutput!

  """
  Query orders with pagination, sorting, and filtering capabilities.

  Example:
  ```graphql
  query {
    orders(query: {
      limit: 20,
      sort: [{ field: "date", order: "desc" }],
      filter: {
        from_source: { eq: "shopify" },
      },
      semantic_search: {
        text: "large orders",
        min_score: 0.5
      }
    }) {
      total_count
      items {
        order_id
        from_source
        channel_id
        date
        total_sales
        gross_sales
        net_sales
        order_items {
          h_item_key
          gross_sales
          net_sales
        }
      }
    }
  }
  ```
  """
  orders(query: OrdersQueryInput!): OrdersQueryOutput!

  """
  Query products with pagination, sorting, and filtering capabilities.

  Example:
  ```graphql
  query {
    products(query: {
      limit: 20,
      sort: [{ field: "name", order: "asc" }],
      filter: {
        status: { eq: "active" },
        price: { gte: 10.0, lte: 100.0 }
      },
      semantic_search: {
        text: "electronics",
        min_score: 0.5
      }
    }) {
      total_count
      items {
        h_key
        name
        price
        status
        categories { name }
      }
    }
  }
  ```
  """
  products(query: ProductsQueryInput!): ProductsQueryOutput!

  """
  Get aggregated sales summary statistics for categories.

  Example:
  ```graphql
  query {
    sales_by_category_summary(query: {
      from_date: "2024-01-01",
      to_date: "2024-12-31"
    }) {
      total_count
      gross_sales
      gross_qty
      stock
      ats
    }
  }
  ```
  """
  sales_by_category_summary(query: SalesByCategorySummaryQueryInput!): SalesByCategorySummary!

  """
  Query sales analytics data aggregated by product category.

  Example:
  ```graphql
  query {
    sales_by_category(query: {
      from_date: "2024-01-01",
      to_date: "2024-12-31",
      sort: [{ field: "gross_sales", order: "desc" }]
    }) {
      h_key
      name
      gross_sales
      gross_qty
      stock
      sell_through
    }
  }
  ```
  """
  sales_by_category(query: SalesByCategoryQueryInput!): [SalesByCategoryItem]

  """
  Get aggregated sales summary statistics for collections.

  Example:
  ```graphql
  query {
    sales_by_collection_summary(query: {
      from_date: "2024-01-01",
      to_date: "2024-12-31"
    }) {
      total_count
      gross_sales
      gross_qty
      stock
      ats
    }
  }
  ```
  """
  sales_by_collection_summary(query: SalesByCollectionSummaryQueryInput!): SalesByCollectionSummary!

  """
  Query sales analytics data aggregated by product collection.

  Example:
  ```graphql
  query {
    sales_by_collection(query: {
      from_date: "2024-01-01",
      to_date: "2024-12-31",
      sort: [{ field: "gross_sales", order: "desc" }]
    }) {
      h_key
      name
      gross_sales
      gross_qty
      stock
      sell_through
    }
  }
  ```
  """
  sales_by_collection(query: SalesByCollectionQueryInput!): [SalesByCollectionItem]

  """
  Get aggregated sales summary statistics for products.

  Example:
  ```graphql
  query {
    sales_by_product_summary(query: {
      from_date: "2024-01-01",
      to_date: "2024-12-31",
      filter: { status: { eq: "active" } }
    }) {
      total_count
      gross_sales
      gross_qty
      net_sales
      stock
      ats
    }
  }
  ```
  """
  sales_by_product_summary(query: SalesByProductSummaryQueryInput!): SalesByProductSummary!

  """
  Query comprehensive sales analytics data for products.

  Example:
  ```graphql
  query {
    sales_by_product(query: {
      from_date: "2024-01-01",
      to_date: "2024-12-31",
      sort: [{ field: "gross_sales", order: "desc" }]
    }) {
      h_key
      name
      gross_sales
      gross_qty
      stock
      ats
    }
  }
  ```
  """
  sales_by_product(query: SalesByProductQueryInput!): [SalesByProductItem]

  """
  Get aggregated sales summary statistics across all variants.

  @deprecated Use sales_by_variant_summary instead.
  """
  sales_summary(query: SalesByVariantSummaryQueryInput!): SalesByVariantSummary!

  """
  Get aggregated sales summary statistics for variants matching the query criteria.

  Example:
  ```graphql
  query {
    sales_by_variant_summary(query: {
      from_date: "2024-01-01",
      to_date: "2024-12-31",
      filter: { status: { eq: "active" } }
    }) {
      total_count
      gross_sales
      gross_qty
      net_sales
      stock
      ats
    }
  }
  ```
  """
  sales_by_variant_summary(query: SalesByVariantSummaryQueryInput!): SalesByVariantSummary!

  """
  Query comprehensive sales analytics data for product variants.
  Returns detailed sales metrics, inventory data, forecasting, and performance indicators.

  Example:
  ```graphql
  query {
    sales_by_variant(query: {
      from_date: "2024-01-01",
      to_date: "2024-12-31",
      limit: 50,
      sort: [{ field: "gross_sales", order: "desc" }],
      filter: {
        status: { eq: "active" },
        gross_sales: { gte: 1000 }
      }
    }) {
      h_key
      name
      sku
      gross_sales
      gross_qty
      stock
      ats
      product_grade
    }
  }
  ```
  """
  sales_by_variant(query: SalesByVariantQueryInput!): [SalesByVariantItem]

  """
  Query variants with pagination, sorting, and filtering capabilities.

  Example:
  ```graphql
  query {
    variants(query: {
      limit: 20,
      sort: [{ field: "sku", order: "asc" }],
      filter: {
        status: { eq: "active" },
        color: { icontains: "blue" },
        price: { gte: 10.0 }
      },
      semantic_search: {
        text: "electronics",
        min_score: 0.5
      }
    }) {
      total_count
      items {
        h_key
        sku
        name
        color
        size
        price
      }
    }
  }
  ```
  """
  variants(query: VariantsQueryInput!): VariantsQueryOutput!
}

"""
Custom JSON scalar type for handling arbitrary JSON data structures.
Used for complex nested data that doesn't fit standard GraphQL types.
"""
scalar JSON

"""
Input type for specifying sort criteria in queries.
Used across all paginated queries to control result ordering.
"""
input SortInput {
  """
  The field name to sort by. Must match a field in the target type.
  """
  field: String!

  """
  Sort direction: 'asc' for ascending, 'desc' for descending.
  """
  order: String!
}

"""
Comprehensive string filtering input with multiple comparison operators.
Supports exact matching, pattern matching, and case-insensitive operations.
"""
input StringFilterInput {
  """
  Full-text search match (implementation-specific).
  """
  match: String

  """
  Exact equality match.
  """
  eq: String

  """
  Not equal to (negation of eq).
  """
  neq: String

  """
  Value must be in the provided list.
  """
  in: [String]

  """
  Value must not be in the provided list.
  """
  nin: [String]

  """
  Case-sensitive substring match.
  """
  contains: String

  """
  Case-sensitive prefix match.
  """
  startswith: String

  """
  Case-sensitive suffix match.
  """
  endswith: String

  """
  Case-insensitive substring match.
  """
  icontains: String

  """
  Case-insensitive prefix match.
  """
  istartswith: String

  """
  Case-insensitive suffix match.
  """
  iendswith: String

  """
  Check if field exists (true) or is null/undefined (false).
  """
  exists: Boolean
}

"""
Numeric filtering input supporting range queries and list operations.
Works with Float, Int, and other numeric types.
"""
input NumericFilterInput {
  """
  Exact equality match.
  """
  eq: Float

  """
  Not equal to (negation of eq).
  """
  neq: Float

  """
  Greater than.
  """
  gt: Float

  """
  Greater than or equal to.
  """
  gte: Float

  """
  Less than.
  """
  lt: Float

  """
  Less than or equal to.
  """
  lte: Float

  """
  Value must be in the provided list.
  """
  in: [Float]

  """
  Value must not be in the provided list.
  """
  nin: [Float]

  """
  Check if field exists (true) or is null/undefined (false).
  """
  exists: Boolean
}

"""
Boolean filtering input for true/false fields.
Supports equality checks and existence validation.
"""
input BooleanFilterInput {
  """
  Exact equality match.
  """
  eq: Boolean

  """
  Not equal to (negation of eq).
  """
  neq: Boolean

  """
  Check if field exists (true) or is null/undefined (false).
  """
  exists: Boolean
}

"""
Comprehensive enum filtering input with multiple comparison operators.
"""
input EnumFilterInput {
  """
  Exact equality match.
  """
  eq: String

  """
  Not equal to (negation of eq).
  """
  neq: String

  """
  Value must be in the provided list.
  """
  in: [String]

  """
  Value must not be in the provided list.
  """
  nin: [String]
}

"""
Revenue grade classification for products based on sales performance.
Used for ABC analysis and inventory prioritization.
"""
enum RevenueGrade {
  """
  High-performing products (top 80% of revenue).
  """
  A

  """
  Medium-performing products (next 15% of revenue).
  """
  B

  """
  Low-performing products (bottom 5% of revenue).
  """
  C
}

input SemanticSearchInput {
  """
  Text to search for in the variant name.
  """
  text: String!

  """
  Minimum score for the search.
  """
  min_score: Float
}

"""
Product category entity representing hierarchical product classification.
Categories are used to organize products into logical groups for navigation and filtering.
"""
type Category {
  """
  Unique hash key identifier for the category.
  """
  h_key: String!

  """
  Business key identifier, typically from the source system.
  """
  b_key: String!

  """
  Source system identifier (e.g., 'shopify', 'magento', 'custom').
  """
  from_source: String!

  """
  Human-readable category name displayed in the UI.
  """
  name: String!
}

"""
Filter input for querying categories with various criteria.
All filters support the full range of string comparison operations.
"""
input CategoriesFilter {
  """
  Filter by category hash key.
  """
  h_key: StringFilterInput

  """
  Filter by business key.
  """
  b_key: StringFilterInput

  """
  Filter by source system.
  """
  from_source: StringFilterInput

  """
  Filter by category name (supports text search).
  """
  name: StringFilterInput
}

"""
Input parameters for querying categories with pagination, sorting, and filtering.
"""
input CategoriesQueryInput {
  """
  Number of records to skip for pagination (0-based).
  """
  offset: Int

  """
  Maximum number of records to return (default: 50, max: 1000).
  """
  limit: Int

  """
  Sort criteria. Multiple sorts are applied in order.
  """
  sort: [SortInput]

  """
  Filter criteria to narrow down results.
  """
  filter: CategoriesFilter

  """
  Semantic search input.
  """
  semantic_search: SemanticSearchInput
}

"""
Response type for category queries containing results and metadata.
"""
type CategoriesQueryOutput {
  """
  Total number of categories matching the filter criteria.
  """
  total_count: Int

  """
  Array of category records for the current page.
  """
  items: [Category]
}

"""
Product collection entity representing curated groups of products.
Collections are typically used for marketing campaigns, seasonal promotions,
or thematic product groupings that cross category boundaries.
"""
type Collection {
  """
  Unique hash key identifier for the collection.
  """
  h_key: String!

  """
  Business key identifier, typically from the source system.
  """
  b_key: String!

  """
  Source system identifier (e.g., 'shopify', 'magento', 'custom').
  """
  from_source: String!

  """
  Human-readable collection name displayed in the UI.
  """
  name: String!
}

"""
Filter input for querying collections with various criteria.
All filters support the full range of string comparison operations.
"""
input CollectionsFilter {
  """
  Filter by collection hash key.
  """
  h_key: StringFilterInput

  """
  Filter by business key.
  """
  b_key: StringFilterInput

  """
  Filter by source system.
  """
  from_source: StringFilterInput

  """
  Filter by collection name (supports text search).
  """
  name: StringFilterInput
}

"""
Input parameters for querying collections with pagination, sorting, and filtering.
"""
input CollectionsQueryInput {
  """
  Number of records to skip for pagination (0-based).
  """
  offset: Int

  """
  Maximum number of records to return (default: 50, max: 1000).
  """
  limit: Int

  """
  Sort criteria. Multiple sorts are applied in order.
  """
  sort: [SortInput]

  """
  Filter criteria to narrow down results.
  """
  filter: CollectionsFilter

  """
  Semantic search input.
  """
  semantic_search: SemanticSearchInput
}

"""
Response type for collection queries containing results and metadata.
"""
type CollectionsQueryOutput {
  """
  Total number of collections matching the filter criteria.
  """
  total_count: Int

  """
  Array of collection records for the current page.
  """
  items: [Collection]
}

"""
Order item entity representing individual items within an order.
"""
type OrderItem {
  """
  Hash key identifier for the item.
  """
  h_item_key: String

  """
  SKU of the item.
  """
  item_sku: String

  """
  Name of the item.
  """
  item_name: String

  """
  Categories associated with the item.
  """
  item_categories: [OrderItemCategories]

  """
  Price per item.
  """
  item_price: Float

  """
  Cost per item.
  """
  cost_per_item: Float

  """
  Tax amount for this item.
  """
  tax: Float

  """
  Net quantity sold (after returns).
  """
  net_qty: Int

  """
  Discount amount applied to this item.
  """
  discount: Float

  """
  Shipping cost for this item.
  """
  shipping: Float

  """
  Gross quantity sold (before returns).
  """
  gross_qty: Int

  """
  Net sales amount (after returns and discounts).
  """
  net_sales: Float

  """
  Quantity returned.
  """
  return_qty: Int

  """
  Gross sales amount (before returns and discounts).
  """
  gross_sales: Float

  """
  Total sales amount including tax.
  """
  total_sales: Float

  """
  Total value of returns for this item.
  """
  return_value: Float

  """
  Total cost for this item.
  """
  cost: Float

  """
  Gross profit for this item.
  """
  gross_profit: Float
}

"""
Categories associated with an order item.
"""
type OrderItemCategories {
  """
  Category name.
  """
  name: String

  """
  Category hash key.
  """
  h_key: String
}

"""
Order entity representing a customer order transaction.
"""
type Order {
  """
  Unique order identifier.
  """
  order_id: String

  """
  Source system identifier (e.g., 'shopify', 'magento', 'custom').
  """
  from_source: String

  """
  Channel identifier where the order was placed.
  """
  channel_id: String

  """
  Order date in YYYY-MM-DD format.
  """
  date: String

  """
  Total sales amount including tax and shipping.
  """
  total_sales: Float

  """
  Net sales amount after returns and discounts.
  """
  net_sales: Float

  """
  Gross sales amount before returns and discounts.
  """
  gross_sales: Float

  """
  Total gross quantity of items sold.
  """
  gross_qty: Int

  """
  Total net quantity of items sold (after returns).
  """
  net_qty: Int

  """
  Total quantity of items returned.
  """
  return_qty: Int

  """
  Total value of returned items.
  """
  return_value: Float

  """
  Total discount amount applied to the order.
  """
  discount: Float

  """
  Total tax amount for the order.
  """
  tax: Float

  """
  Total shipping cost for the order.
  """
  shipping: Float

  """
  Number of unique items in the order.
  """
  unique_item_count: Int

  """
  Total gross profit for the order.
  """
  gross_profit: Float

  """
  Individual items within the order.
  """
  order_items: [OrderItem]
}

"""
Filter input for querying orders with various criteria.
All filters support the full range of string and numeric comparison operations.
"""
input OrdersFilter {
  """
  Filter by order ID.
  """
  order_id: StringFilterInput

  """
  Filter by source system.
  """
  from_source: StringFilterInput

  """
  Filter by channel ID.
  """
  channel_id: StringFilterInput

  """
  Filter by total sales amount.
  """
  total_sales: NumericFilterInput

  """
  Filter by net sales amount.
  """
  net_sales: NumericFilterInput

  """
  Filter by gross sales.
  """
  gross_sales: NumericFilterInput

  """
  Filter by gross quantity.
  """
  gross_qty: NumericFilterInput

  """
  Filter by net quantity.
  """
  net_qty: NumericFilterInput

  """
  Filter by return quantity.
  """
  return_qty: NumericFilterInput

  """
  Filter by return value.
  """
  return_value: NumericFilterInput

  """
  Filter by discount amount.
  """
  discount: NumericFilterInput

  """
  Filter by tax amount.
  """
  tax: NumericFilterInput

  """
  Filter by shipping cost.
  """
  shipping: NumericFilterInput

  """
  Filter by unique item count.
  """
  unique_item_count: NumericFilterInput

  """
  Filter by gross profit.
  """
  gross_profit: NumericFilterInput

  """
  Filter by item h_key.
  """
  h_item_key: StringFilterInput

  """
  Filter by variant name.
  """
  variant_name: StringFilterInput

  """
  Filter by variant SKU.
  """
  variant_sku: StringFilterInput

  """
  Filter by variant ID.
  """
  variant_id: StringFilterInput

  """
  Filter by barcode.
  """
  variant_barcode: StringFilterInput

  """
  Filter by category h_keys.
  """
  variant_categories_h_key: StringFilterInput

  """
  Filter by collection hash keys.
  """
  variant_collections_h_key: StringFilterInput

  """
  Filter by category names.
  """
  variant_categories_name: StringFilterInput

  """
  Filter by collection names.
  """
  variant_collections_name: StringFilterInput
}

"""
Input parameters for querying orders with pagination, sorting, and filtering.
"""
input OrdersQueryInput {
  """
  Number of records to skip for pagination (0-based).
  """
  offset: Int

  """
  Maximum number of records to return (default: 50, max: 1000).
  """
  limit: Int

  """
  Sort criteria. Multiple sorts are applied in order.
  """
  sort: [SortInput]

  """
  Filter criteria to narrow down results.
  """
  filter: OrdersFilter

  """
  Start date for the analysis period (ISO 8601 format).
  """
  from_date: String

  """
  End date for the analysis period (ISO 8601 format).
  """
  to_date: String
}

"""
Response type for order queries containing results and metadata.
"""
type OrdersQueryOutput {
  """
  Total number of orders matching the filter criteria.
  """
  total_count: Int

  """
  Total gross sales amount across all matching orders.
  """
  total_gross_sales: Float

  """
  Total gross quantity across all matching orders.
  """
  total_gross_qty: Int

  """
  Total net sales amount across all matching orders.
  """
  total_net_sales: Float

  """
  Total net quantity across all matching orders.
  """
  total_net_qty: Int

  """
  Total sales amount (including tax and shipping) across all matching orders.
  """
  total_total_sales: Float

  """
  Total tax amount across all matching orders.
  """
  total_tax: Float

  """
  Total discount amount across all matching orders.
  """
  total_discount: Float

  """
  Total return quantity across all matching orders.
  """
  total_return_qty: Int

  """
  Total return value across all matching orders.
  """
  total_return_value: Float

  """
  Total gross profit across all matching orders.
  """
  total_gross_profit: Float

  """
  Array of order records for the current page.
  """
  items: [Order]
}

"""
Product entity representing a master product record.
Products can have multiple variants and are organized into categories and collections.
Contains core product information, pricing, and metadata.
"""
type Product {
  """
  Unique hash key identifier for the product.
  """
  h_key: String!

  """
  Source system identifier (e.g., 'shopify', 'magento', 'custom').
  """
  from_source: String

  """
  Hash key of the parent product (for product hierarchies).
  """
  parent_h_key: String

  """
  Business key identifier, typically from the source system.
  """
  b_key: String

  """
  Variant identifier from the source system.
  """
  variant_id: String

  """
  Product identifier from the source system.
  """
  product_id: String

  """
  Product type classification (e.g., 'simple', 'configurable', 'bundle').
  """
  type: String

  """
  Product name displayed to customers.
  """
  name: String

  """
  Stock Keeping Unit - unique identifier for inventory tracking.
  """
  sku: String

  """
  Product barcode for scanning and identification.
  """
  barcode: String

  """
  Cost per item for margin calculations (in base currency).
  """
  cost_per_item: Float

  """
  Current selling price (in base currency).
  """
  price: Float

  """
  Original price before discounts (in base currency).
  """
  compare_at_price: Float

  """
  URL or path to the primary product image.
  """
  image: String

  """
  Product color attribute.
  """
  color: String

  """
  Default color for the product line.
  """
  default_color: String

  """
  Product size attribute.
  """
  size: String

  """
  Product status (e.g., 'active', 'draft', 'archived').
  """
  status: String

  """
  Timestamp when the product was created in the source system.
  """
  source_created_at: String

  """
  Timestamp when the product was published/made available.
  """
  published_at: String

  """
  Categories this product belongs to.
  """
  categories: [VariantCategory]

  """
  Collections this product is part of.
  """
  collections: [VariantCollection]

  """
  Tags associated with this product.
  """
  tags: [VariantTag]

  """
  Vendors/suppliers for this product.
  """
  vendors: [VariantVendor]
}

"""
Filter input for querying products with various criteria.
Supports filtering by product attributes, pricing, and associated entities.
"""
input ProductsFilter {
  """
  Filter by product hash key.
  """
  h_key: StringFilterInput

  """
  Filter by business key.
  """
  b_key: StringFilterInput

  """
  Filter by source system.
  """
  from_source: StringFilterInput

  """
  Filter by product ID from source system.
  """
  product_id: StringFilterInput

  """
  Filter by product name (supports text search).
  """
  name: StringFilterInput

  """
  Filter by product status.
  """
  status: StringFilterInput

  """
  Filter by current selling price.
  """
  price: NumericFilterInput

  """
  Filter by compare-at price.
  """
  compare_at_price: NumericFilterInput

  """
  Filter by associated category hash keys.
  """
  categories_h_key: StringFilterInput

  """
  Filter by associated collection hash keys.
  """
  collections_h_key: StringFilterInput
}

"""
Input parameters for querying products with pagination, sorting, and filtering.
"""
input ProductsQueryInput {
  """
  Number of records to skip for pagination (0-based).
  """
  offset: Int

  """
  Maximum number of records to return (default: 50, max: 1000).
  """
  limit: Int

  """
  Sort criteria. Multiple sorts are applied in order.
  """
  sort: [SortInput]

  """
  Filter criteria to narrow down results.
  """
  filter: ProductsFilter

  """
  Semantic search input.
  """
  semantic_search: SemanticSearchInput
}

"""
Response type for product queries containing results and metadata.
"""
type ProductsQueryOutput {
  """
  Total number of products matching the filter criteria.
  """
  total_count: Int

  """
  Array of product records for the current page.
  """
  items: [Product]
}

"""
Aggregated summary statistics for sales-by-category analytics.
Provides totals across all categories matching the query criteria.
"""
type SalesByCategorySummary {
  """
  Total number of categories included in the summary.
  """
  total_count: Int!

  """
  Total gross sales revenue across all categories.
  """
  gross_sales: Float

  """
  Total gross quantity sold across all categories.
  """
  gross_qty: Int

  """
  Total net sales after returns across all categories.
  """
  net_sales: Float

  """
  Total net quantity after returns across all categories.
  """
  net_qty: Int

  """
  Total sales including taxes, shipping, etc. across all categories.
  """
  total_sales: Float

  """
  Total return value across all categories.
  """
  return_value: Float

  """
  Total return quantity across all categories.
  """
  return_qty: Int

  """
  Total advertising spend across all categories.
  """
  ad_spends: Float

  """
  Total ad clicks across all categories.
  """
  clicks: Int

  """
  Total page views across all categories.
  """
  views: Int

  """
  Total stock quantity across all categories.
  """
  stock: Int

  """
  Total on-order quantity across all categories.
  """
  on_order: Int

  """
  Total available-to-sell quantity across all categories.
  """
  ats: Int

  """
  Total inventory value based on stock across all categories.
  """
  total_inventory_value_stock: Float

  """
  Total inventory value based on ATS across all categories.
  """
  total_inventory_value_ats: Float

  """
  Average sell-through rate across all categories.
  """
  sell_through: Float
}

"""
Input parameters for querying sales-by-category summary statistics.
"""
input SalesByCategorySummaryQueryInput {
  """
  Start date for analysis period.
  """
  from_date: String

  """
  End date for analysis period.
  """
  to_date: String

  """
  Start date for forecast analysis.
  """
  forecast_from_date: String

  """
  End date for forecast analysis.
  """
  forecast_to_date: String

  """
  Text search across category names.
  """
  search: String

  """
  Filter criteria to narrow down which categories are included.
  """
  filter: SalesByCategoryFilter
}

"""
Sales analytics data aggregated by product category.
Provides category-level insights for sales performance and inventory management.
"""
type SalesByCategoryItem {
  """
  Unique hash key identifier for the category.
  """
  h_key: String!

  """
  Category name.
  """
  name: String

  """
  Total gross sales revenue for this category.
  """
  gross_sales: Float

  """
  Total gross quantity sold for this category.
  """
  gross_qty: Int

  """
  Total net sales after returns for this category.
  """
  net_sales: Float

  """
  Total net quantity after returns for this category.
  """
  net_qty: Int

  """
  Total sales including taxes, shipping, etc. for this category.
  """
  total_sales: Float

  """
  Total return value for this category.
  """
  return_value: Float

  """
  Total return quantity for this category.
  """
  return_qty: Int

  """
  Percentage of total stock this category represents.
  """
  stock_percentage: Float

  """
  Sell-through rate for this category.
  """
  sell_through: Float

  """
  Percentage of total gross sales this category represents.
  """
  gross_sales_percentage: Float

  """
  Total advertising spend for this category.
  """
  ad_spends: Float

  """
  Total ad clicks for this category.
  """
  clicks: Int

  """
  Total page views for this category.
  """
  views: Int

  """
  Total stock quantity for this category.
  """
  stock: Int

  """
  Total on-order quantity for this category.
  """
  on_order: Int

  """
  Total available-to-sell quantity for this category.
  """
  ats: Int

  """
  Total inventory value based on stock for this category.
  """
  total_inventory_value_stock: Float

  """
  Total inventory value based on ATS for this category.
  """
  total_inventory_value_ats: Float
}

"""
Filter input for sales-by-category queries.
Allows filtering category analytics data by various criteria including sales metrics,
inventory levels, and performance indicators.
"""
input SalesByCategoryFilter {
  """
  Filter by category hash key identifier.
  """
  h_key: StringFilterInput

  """
  Filter by category name.
  """
  name: StringFilterInput

  """
  Filter by total gross sales revenue range.
  """
  gross_sales: NumericFilterInput

  """
  Filter by total gross quantity sold range.
  """
  gross_qty: NumericFilterInput

  """
  Filter by total net sales after returns range.
  """
  net_sales: NumericFilterInput

  """
  Filter by total net quantity after returns range.
  """
  net_qty: NumericFilterInput

  """
  Filter by total sales including taxes and shipping range.
  """
  total_sales: NumericFilterInput

  """
  Filter by total return value range.
  """
  return_value: NumericFilterInput

  """
  Filter by total return quantity range.
  """
  return_qty: NumericFilterInput

  """
  Filter by total ad clicks range.
  """
  clicks: NumericFilterInput

  """
  Filter by total page views range.
  """
  views: NumericFilterInput

  """
  Filter by total stock quantity range.
  """
  stock: NumericFilterInput

  """
  Filter by total on-order quantity range.
  """
  on_order: NumericFilterInput

  """
  Filter by total available-to-sell quantity range.
  """
  ats: NumericFilterInput

  """
  Filter by total inventory value based on stock range.
  """
  total_inventory_value_stock: NumericFilterInput

  """
  Filter by total inventory value based on ATS range.
  """
  total_inventory_value_ats: NumericFilterInput

  """
  Filter by stock percentage of total inventory range.
  """
  stock_percentage: NumericFilterInput

  """
  Filter by gross sales percentage of total sales range.
  """
  gross_sales_percentage: NumericFilterInput
}

"""
Input parameters for querying sales-by-category analytics.
"""
input SalesByCategoryQueryInput {
  """
  Number of records to skip for pagination.
  """
  offset: Int

  """
  Maximum number of records to return.
  """
  limit: Int

  """
  Start date for analysis period.
  """
  from_date: String

  """
  End date for analysis period.
  """
  to_date: String

  """
  Start date for forecast analysis.
  """
  forecast_from_date: String

  """
  End date for forecast analysis.
  """
  forecast_to_date: String

  """
  Sort criteria.
  """
  sort: [SortInput]

  """
  Text search across category names.
  """
  search: String

  """
  Filter criteria.
  """
  filter: SalesByCategoryFilter
}

"""
Aggregated summary statistics for sales-by-collection analytics.
Provides totals across all collections matching the query criteria.
"""
type SalesByCollectionSummary {
  """
  Total number of collections included in the summary.
  """
  total_count: Int!

  """
  Total gross sales revenue across all collections.
  """
  gross_sales: Float

  """
  Total gross quantity sold across all collections.
  """
  gross_qty: Int

  """
  Total net sales after returns across all collections.
  """
  net_sales: Float

  """
  Total net quantity after returns across all collections.
  """
  net_qty: Int

  """
  Total sales including taxes, shipping, etc. across all collections.
  """
  total_sales: Float

  """
  Total return value across all collections.
  """
  return_value: Float

  """
  Total return quantity across all collections.
  """
  return_qty: Int

  """
  Total advertising spend across all collections.
  """
  ad_spends: Float

  """
  Total ad clicks across all collections.
  """
  clicks: Int

  """
  Total page views across all collections.
  """
  views: Int

  """
  Total stock quantity across all collections.
  """
  stock: Int

  """
  Total on-order quantity across all collections.
  """
  on_order: Int

  """
  Total available-to-sell quantity across all collections.
  """
  ats: Int

  """
  Total inventory value based on stock across all collections.
  """
  total_inventory_value_stock: Float

  """
  Total inventory value based on ATS across all collections.
  """
  total_inventory_value_ats: Float

  """
  Average sell-through rate across all collections.
  """
  sell_through: Float
}

"""
Input parameters for querying sales-by-collection summary statistics.
"""
input SalesByCollectionSummaryQueryInput {
  """
  Start date for analysis period.
  """
  from_date: String

  """
  End date for analysis period.
  """
  to_date: String

  """
  Start date for forecast analysis.
  """
  forecast_from_date: String

  """
  End date for forecast analysis.
  """
  forecast_to_date: String

  """
  Text search across collection names.
  """
  search: String

  """
  Filter criteria to narrow down which collections are included.
  """
  filter: SalesByCollectionFilter
}

"""
Sales analytics data aggregated by product collection.
Provides collection-level insights for sales performance and inventory management.
"""
type SalesByCollectionItem {
  """
  Unique hash key identifier for the collection.
  """
  h_key: String!

  """
  Collection name.
  """
  name: String

  """
  Total gross sales revenue for this collection.
  """
  gross_sales: Float

  """
  Total gross quantity sold for this collection.
  """
  gross_qty: Int

  """
  Total net sales after returns for this collection.
  """
  net_sales: Float

  """
  Total net quantity after returns for this collection.
  """
  net_qty: Int

  """
  Total sales including taxes, shipping, etc. for this collection.
  """
  total_sales: Float

  """
  Total return value for this collection.
  """
  return_value: Float

  """
  Total return quantity for this collection.
  """
  return_qty: Int

  """
  Total advertising spend for this collection.
  """
  ad_spends: Float

  """
  Total ad clicks for this collection.
  """
  clicks: Int

  """
  Total page views for this collection.
  """
  views: Int

  """
  Total stock quantity for this collection.
  """
  stock: Int

  """
  Total on-order quantity for this collection.
  """
  on_order: Int

  """
  Total available-to-sell quantity for this collection.
  """
  ats: Int

  """
  Total inventory value based on stock for this collection.
  """
  total_inventory_value_stock: Float

  """
  Total inventory value based on ATS for this collection.
  """
  total_inventory_value_ats: Float

  """
  Sell-through rate for this collection.
  """
  sell_through: Float

  """
  Percentage of total stock this collection represents.
  """
  stock_percentage: Float

  """
  Percentage of total gross sales this collection represents.
  """
  gross_sales_percentage: Float
}

"""
Filter input for sales-by-collection queries.
Allows filtering collection analytics data by various criteria including sales metrics,
inventory levels, and performance indicators.
"""
input SalesByCollectionFilter {
  """
  Filter by collection hash key identifier.
  """
  h_key: StringFilterInput

  """
  Filter by collection name.
  """
  name: StringFilterInput

  """
  Filter by total gross sales revenue range.
  """
  gross_sales: NumericFilterInput

  """
  Filter by total gross quantity sold range.
  """
  gross_qty: NumericFilterInput

  """
  Filter by total net sales after returns range.
  """
  net_sales: NumericFilterInput

  """
  Filter by total net quantity after returns range.
  """
  net_qty: NumericFilterInput

  """
  Filter by total sales including taxes and shipping range.
  """
  total_sales: NumericFilterInput

  """
  Filter by total return value range.
  """
  return_value: NumericFilterInput

  """
  Filter by total return quantity range.
  """
  return_qty: NumericFilterInput

  """
  Filter by total ad clicks range.
  """
  clicks: NumericFilterInput

  """
  Filter by total page views range.
  """
  views: NumericFilterInput

  """
  Filter by total stock quantity range.
  """
  stock: NumericFilterInput

  """
  Filter by total on-order quantity range.
  """
  on_order: NumericFilterInput

  """
  Filter by total available-to-sell quantity range.
  """
  ats: NumericFilterInput

  """
  Filter by total inventory value based on stock range.
  """
  total_inventory_value_stock: NumericFilterInput

  """
  Filter by total inventory value based on ATS range.
  """
  total_inventory_value_ats: NumericFilterInput

  """
  Filter by stock percentage of total inventory range.
  """
  stock_percentage: NumericFilterInput

  """
  Filter by gross sales percentage of total sales range.
  """
  gross_sales_percentage: NumericFilterInput
}

"""
Input parameters for querying sales-by-collection analytics.
"""
input SalesByCollectionQueryInput {
  """
  Number of records to skip for pagination.
  """
  offset: Int

  """
  Maximum number of records to return.
  """
  limit: Int

  """
  Start date for analysis period.
  """
  from_date: String

  """
  End date for analysis period.
  """
  to_date: String

  """
  Start date for forecast analysis.
  """
  forecast_from_date: String

  """
  End date for forecast analysis.
  """
  forecast_to_date: String

  """
  Sort criteria.
  """
  sort: [SortInput]

  """
  Text search across collection names.
  """
  search: String

  """
  Filter criteria.
  """
  filter: SalesByCollectionFilter
}

"""
Aggregated summary statistics for sales-by-product analytics.
Provides totals and averages across all products matching the query criteria.
"""
type SalesByProductSummary {
  """
  Total number of products included in the summary.
  """
  total_count: Int!

  """
  Total gross sales revenue across all products.
  """
  gross_sales: Float

  """
  Total gross quantity sold across all products.
  """
  gross_qty: Int

  """
  Total net sales after returns across all products.
  """
  net_sales: Float

  """
  Total net quantity after returns across all products.
  """
  net_qty: Int

  """
  Total sales including taxes, shipping, etc. across all products.
  """
  total_sales: Float

  """
  Total return value across all products.
  """
  return_value: Float

  """
  Total return quantity across all products.
  """
  return_qty: Int

  """
  Average return rate across all products.
  """
  return_rate: Float

  """
  Total discount amount across all products.
  """
  discount: Float

  """
  Total tax amount across all products.
  """
  tax: Float

  """
  Total shipping charges across all products.
  """
  shipping: Float

  """
  Total advertising spend across all products.
  """
  ad_spends: Float

  """
  Total ad clicks across all products.
  """
  clicks: Int

  """
  Total page views across all products.
  """
  views: Int

  """
  Total stock quantity across all products.
  """
  stock: Int

  """
  Total on-order quantity across all products.
  """
  on_order: Int

  """
  Total available-to-sell quantity across all products.
  """
  ats: Int

  """
  Total gross profit across all products.
  """
  gross_profit: Float

  """
  Total inventory value (stock-based) across all products.
  """
  total_inventory_value_stock: Float

  """
  Total inventory value (ATS-based) across all products.
  """
  total_inventory_value_ats: Float
}

"""
Input parameters for querying sales-by-product summary statistics.
"""
input SalesByProductSummaryQueryInput {
  """
  Start date for analysis period.
  """
  from_date: String

  """
  End date for analysis period.
  """
  to_date: String

  """
  Start date for forecast analysis.
  """
  forecast_from_date: String

  """
  End date for forecast analysis.
  """
  forecast_to_date: String

  """
  Text search across product names, SKUs, etc.
  """
  search: String

  """
  Filter criteria to narrow down which products are included.
  """
  filter: SalesByProductFilter

  """
  Channel ID to filter by specific sales channel.
  """
  channel_id: String
}

"""
Collection information associated with products in sales analytics.
"""
type ProductCollection {
  """
  Source system identifier.
  """
  from_source: String!

  """
  Collection name.
  """
  name: String!

  """
  Position of the product within the collection.
  """
  position: Int!

  """
  Unique hash key identifier for the collection.
  """
  h_key: String!
}

"""
Category information associated with products in sales analytics.
"""
type ProductCategory {
  """
  Source system identifier.
  """
  from_source: String!

  """
  Category name.
  """
  name: String!

  """
  Unique hash key identifier for the category.
  """
  h_key: String!
}

"""
Tag information associated with products in sales analytics.
"""
type ProductTag {
  """
  Source system identifier.
  """
  from_source: String!

  """
  Tag name.
  """
  name: String!

  """
  Unique hash key identifier for the tag.
  """
  h_key: String!
}

"""
Vendor/supplier information associated with products in sales analytics.
"""
type ProductVendor {
  """
  Source system identifier.
  """
  from_source: String!

  """
  Vendor name.
  """
  name: String!

  """
  Unique hash key identifier for the vendor.
  """
  h_key: String!
}

"""
Comprehensive sales analytics data for individual products.
Contains detailed sales metrics, inventory data, forecasting, and performance indicators.
This is the primary entity for product-level business intelligence and reporting.
"""
type SalesByProductItem {
  """
  Unique hash key identifier for the product.
  """
  h_key: String!

  """
  Business key identifier.
  """
  b_key: String

  """
  Categories this product belongs to.
  """
  categories: [ProductCategory]

  """
  Collections this product is part of.
  """
  collections: [ProductCollection]

  """
  Original price before discounts.
  """
  compare_at_price: Float

  """
  Number of days the product has been available.
  """
  days_on_site: Int

  """
  DDA (Dynamic Data Attribute) code.
  """
  dda_code: String

  """
  DDA (Dynamic Data Attribute) value.
  """
  dda_value: String

  """
  Source system identifier.
  """
  from_source: String

  """
  Product image URL.
  """
  image: String

  """
  Product name.
  """
  name: String

  """
  Quantity on order from suppliers.
  """
  on_order: Int

  """
  Current selling price.
  """
  price: Float

  """
  Hash key of the parent product.
  """
  product_h_key: String

  """
  Product identifier from source system.
  """
  product_id: String

  """
  Whether the product can be replenished.
  """
  replenishable: Boolean

  """
  Timestamp when created in source system.
  """
  source_created_at: String

  """
  Product status.
  """
  status: String

  """
  Current stock quantity.
  """
  stock: Int

  """
  Tags associated with this product.
  """
  tags: [ProductTag]

  """
  Product type classification.
  """
  type: String

  """
  Vendors/suppliers for this product.
  """
  vendors: [ProductVendor]

  """
  Available-to-sell quantity.
  """
  ats: Float

  """
  Total discount amount applied.
  """
  discount: Float

  """
  Total quantity sold (gross).
  """
  gross_qty: Int

  """
  Total gross sales revenue.
  """
  gross_sales: Float

  """
  Net quantity after returns.
  """
  net_qty: Int

  """
  Net sales after returns.
  """
  net_sales: Float

  """
  Total quantity returned.
  """
  return_qty: Int

  """
  Return rate percentage.
  """
  return_rate: Float

  """
  Total value of returns.
  """
  return_value: Float

  """
  Average sales per day.
  """
  sales_per_day: Float

  """
  Total shipping charges.
  """
  shipping: Float

  """
  Total tax amount.
  """
  tax: Float

  """
  Total sales including all charges.
  """
  total_sales: Float

  """
  Estimated days of sales remaining.
  """
  sales_days_left: Float

  """
  Sell-through rate.
  """
  sell_through: Float

  """
  Gross profit.
  """
  gross_profit: Float

  """
  Gross margin percentage.
  """
  gross_margin: Float

  """
  Cost per item.
  """
  cost_per_item: Float

  """
  Total inventory value based on current stock.
  """
  total_inventory_value_stock: Float

  """
  Total inventory value (ATS-based).
  """
  total_inventory_value_ats: Float

  """
  Weeks of supply remaining.
  """
  wos: Float

  """
  Recommended reorder quantity.
  """
  re_order_qty: Int

  """
  Percentage of total stock.
  """
  stock_percentage: Float

  """
  Reorder point.
  """
  rop: Float

  """
  Lead time in days.
  """
  lead_time: Int

  """
  Days of stock remaining.
  """
  days_of_stock: Int

  """
  Percentage of total gross sales.
  """
  gross_sales_percentage: Float

  """
  Total advertising spend.
  """
  ad_spends: Float

  """
  Number of ad clicks.
  """
  clicks: Int

  """
  Number of page views.
  """
  views: Int

  """
  Conversion rate.
  """
  conversion_rate: Float

  """
  Sales trend indicator.
  """
  trend: String

  """
  Sales pattern classification.
  """
  sale_pattern: String

  """
  Forecasting confidence score.
  """
  confidence_score: String

  """
  Forecasted sales value.
  """
  forecast_value: Int

  """
  Forecasted sales per day.
  """
  forecast_sales_per_day: Int

  """
  Forecasted gross quantity.
  """
  forecasted_gross_qty: Int

  """
  Forecasted gross sales.
  """
  forecasted_gross_sales: Float

  """
  Forecasted sales days remaining (stock-based).
  """
  forecasted_sales_days_remaining_based_on_stock: Int

  """
  Forecasted sales days remaining (ATS-based).
  """
  forecasted_sales_days_remaining_based_on_ats: Int

  """
  Product grade classification.
  """
  product_grade: RevenueGrade

  """
  Position within collection.
  """
  collection_position: Int
}

"""
Comprehensive filter input for sales-by-product queries.
Allows filtering on product attributes, sales metrics, inventory data, and forecasting fields.
All filters are optional and can be combined to create complex query conditions.
"""
input SalesByProductFilter {
  """
  Filter by product hash key identifier.
  """
  h_key: StringFilterInput

  """
  Filter by business key identifier.
  """
  b_key: StringFilterInput

  """
  Filter by original price before discounts.
  """
  compare_at_price: NumericFilterInput

  """
  Filter by cost per item.
  """
  cost_per_item: NumericFilterInput

  """
  Filter by number of days the product has been available.
  """
  days_on_site: NumericFilterInput

  """
  Filter by DDA (Dynamic Data Attribute) code.
  """
  dda_code: StringFilterInput

  """
  Filter by DDA (Dynamic Data Attribute) value.
  """
  dda_value: StringFilterInput

  """
  Filter by source system identifier.
  """
  from_source: StringFilterInput

  """
  Filter by product image URL.
  """
  image: StringFilterInput

  """
  Filter by product name.
  """
  name: StringFilterInput

  """
  Filter by quantity on order from suppliers.
  """
  on_order: NumericFilterInput

  """
  Filter by current selling price.
  """
  price: NumericFilterInput

  """
  Filter by parent product hash key.
  """
  product_h_key: StringFilterInput

  """
  Filter by product identifier from source system.
  """
  product_id: StringFilterInput

  """
  Filter by whether the product can be replenished.
  """
  replenishable: BooleanFilterInput

  """
  Filter by timestamp when created in source system.
  """
  source_created_at: StringFilterInput

  """
  Filter by product status.
  """
  status: StringFilterInput

  """
  Filter by current stock quantity.
  """
  stock: NumericFilterInput

  """
  Filter by total inventory value based on stock.
  """
  total_inventory_value_stock: NumericFilterInput

  """
  Filter by product type classification.
  """
  type: StringFilterInput

  """
  Filter by available-to-sell quantity.
  """
  ats: NumericFilterInput

  """
  Filter by total discount amount applied.
  """
  discount: NumericFilterInput

  """
  Filter by total quantity sold (gross).
  """
  gross_qty: NumericFilterInput

  """
  Filter by total gross sales revenue.
  """
  gross_sales: NumericFilterInput

  """
  Filter by net quantity after returns.
  """
  net_qty: NumericFilterInput

  """
  Filter by net sales after returns.
  """
  net_sales: NumericFilterInput

  """
  Filter by total quantity returned.
  """
  return_qty: NumericFilterInput

  """
  Filter by return rate percentage.
  """
  return_rate: NumericFilterInput

  """
  Filter by total value of returns.
  """
  return_value: NumericFilterInput

  """
  Filter by average sales per day.
  """
  sales_per_day: NumericFilterInput

  """
  Filter by total shipping charges.
  """
  shipping: NumericFilterInput

  """
  Filter by total tax amount.
  """
  tax: NumericFilterInput

  """
  Filter by total sales including all charges.
  """
  total_sales: NumericFilterInput

  """
  Filter by total advertising spend.
  """
  ad_spends: NumericFilterInput

  """
  Filter by number of ad clicks.
  """
  clicks: NumericFilterInput

  """
  Filter by number of page views.
  """
  views: NumericFilterInput

  """
  Filter by category hash key identifier.
  """
  categories_h_key: StringFilterInput

  """
  Filter by collection hash key identifier.
  """
  collections_h_key: StringFilterInput

  """
  Filter by category name.
  """
  categories_name: StringFilterInput

  """
  Filter by collection name.
  """
  collections_name: StringFilterInput

  """
  Filter by sales trend indicator.
  """
  trend: StringFilterInput

  """
  Filter by sales pattern classification.
  """
  sale_pattern: StringFilterInput

  """
  Filter by forecasted sales value.
  """
  forecast_value: NumericFilterInput

  """
  Filter by forecasted sales per day.
  """
  forecast_sales_per_day: NumericFilterInput

  """
  Filter by forecasted gross quantity.
  """
  forecasted_gross_qty: NumericFilterInput

  """
  Filter by forecasted gross sales.
  """
  forecasted_gross_sales: NumericFilterInput

  """
  Filter by forecasted sales days remaining (stock-based).
  """
  forecasted_sales_days_remaining_based_on_stock: NumericFilterInput

  """
  Filter by forecasted sales days remaining (ATS-based).
  """
  forecasted_sales_days_remaining_based_on_ats: NumericFilterInput

  """
  Filter by percentage of total stock.
  """
  stock_percentage: NumericFilterInput

  """
  Filter by percentage of total gross sales.
  """
  gross_sales_percentage: NumericFilterInput

  """
  Filter by product grade classification.
  """
  product_grade: EnumFilterInput
}

"""
Input parameters for querying sales-by-product analytics.
"""
input SalesByProductQueryInput {
  """
  Number of records to skip for pagination.
  """
  offset: Int

  """
  Maximum number of records to return.
  """
  limit: Int

  """
  Start date for analysis period.
  """
  from_date: String

  """
  End date for analysis period.
  """
  to_date: String

  """
  Start date for forecast analysis.
  """
  forecast_from_date: String

  """
  End date for forecast analysis.
  """
  forecast_to_date: String

  """
  Sort criteria.
  """
  sort: [SortInput]

  """
  Text search across product names, SKUs, etc.
  """
  search: String

  """
  Filter criteria.
  """
  filter: SalesByProductFilter
}

"""
Aggregated summary statistics for sales-by-variant analytics.
Provides totals and averages across all variants matching the query criteria.
"""
type SalesByVariantSummary {
  """
  Total number of variants included in the summary.
  """
  total_count: Int!

  """
  Total gross sales revenue across all variants.
  """
  gross_sales: Float

  """
  Total gross quantity sold across all variants.
  """
  gross_qty: Int

  """
  Total net sales after returns across all variants.
  """
  net_sales: Float

  """
  Total net quantity after returns across all variants.
  """
  net_qty: Int

  """
  Total sales including taxes, shipping, etc. across all variants.
  """
  total_sales: Float

  """
  Total return value across all variants.
  """
  return_value: Float

  """
  Total return quantity across all variants.
  """
  return_qty: Int

  """
  Average return rate across all variants.
  """
  return_rate: Float

  """
  Total discount amount across all variants.
  """
  discount: Float

  """
  Total tax amount across all variants.
  """
  tax: Float

  """
  Total shipping charges across all variants.
  """
  shipping: Float

  """
  Total advertising spend across all variants.
  """
  ad_spends: Float

  """
  Total ad clicks across all variants.
  """
  clicks: Int

  """
  Total page views across all variants.
  """
  views: Int

  """
  Total stock quantity across all variants.
  """
  stock: Int

  """
  Total on-order quantity across all variants.
  """
  on_order: Int

  """
  Total available-to-sell quantity across all variants.
  """
  ats: Int

  """
  Total gross profit across all variants.
  """
  gross_profit: Float

  """
  Total inventory value (stock-based) across all variants.
  """
  total_inventory_value_stock: Float

  """
  Total inventory value (ATS-based) across all variants.
  """
  total_inventory_value_ats: Float
}

"""
Input parameters for querying sales-by-variant summary statistics.
"""
input SalesByVariantSummaryQueryInput {
  """
  Start date for the analysis period (ISO 8601 format).
  """
  from_date: String

  """
  End date for the analysis period (ISO 8601 format).
  """
  to_date: String

  """
  Start date for forecast analysis (ISO 8601 format).
  """
  forecast_from_date: String

  """
  End date for forecast analysis (ISO 8601 format).
  """
  forecast_to_date: String

  """
  Text search across variant names, SKUs, and other searchable fields.
  """
  search: String

  """
  Filter criteria to narrow down which variants are included in the summary.
  """
  filter: SalesByVariantFilter

  """
  Channel ID to filter results by specific sales channel.
  """
  channel_id: String
}

"""
Collection information associated with variants in sales analytics.
Includes positioning data for merchandising insights.
"""
type VariantCollection {
  """
  Source system identifier.
  """
  from_source: String!

  """
  Collection name.
  """
  name: String!

  """
  Position of the variant within the collection.
  """
  position: Int!

  """
  Unique hash key identifier for the collection.
  """
  h_key: String!
}

"""
Category information associated with variants in sales analytics.
"""
type VariantCategory {
  """
  Source system identifier.
  """
  from_source: String!

  """
  Category name.
  """
  name: String!

  """
  Unique hash key identifier for the category.
  """
  h_key: String!
}

"""
Tag information associated with variants in sales analytics.
"""
type VariantTag {
  """
  Source system identifier.
  """
  from_source: String!

  """
  Tag name.
  """
  name: String!

  """
  Unique hash key identifier for the tag.
  """
  h_key: String!
}

"""
Vendor/supplier information associated with variants in sales analytics.
"""
type VariantVendor {
  """
  Source system identifier.
  """
  from_source: String!

  """
  Vendor name.
  """
  name: String!

  """
  Unique hash key identifier for the vendor.
  """
  h_key: String!
}

"""
Comprehensive sales analytics data for individual product variants.
Contains detailed sales metrics, inventory data, forecasting, and performance indicators.
This is the primary entity for variant-level business intelligence and reporting.
"""
type SalesByVariantItem {
  """
  Unique hash key identifier for the variant.
  """
  h_key: String!

  """
  Source system identifier (e.g., 'shopify', 'magento', 'big-commerce',...).
  """
  from_source: String

  """
  Hash key of the parent product this variant belongs to.
  """
  parent_h_key: String

  """
  Business key identifier, typically from the source system.
  """
  b_key: String

  """
  Variant identifier from the source system.
  """
  variant_id: String

  """
  Product identifier from the source system.
  """
  product_id: String

  """
  Hash key of the parent product.
  """
  product_h_key: String

  """
  Variant type classification.
  """
  type: String

  """
  Variant name displayed to customers.
  """
  name: String

  """
  Stock Keeping Unit - unique identifier for inventory tracking.
  """
  sku: String

  """
  Variant barcode for scanning and identification.
  """
  barcode: String

  """
  Current selling price (in base currency).
  """
  price: Float

  """
  Original price before discounts (in base currency).
  """
  compare_at_price: Float

  """
  URL or path to the variant-specific image.
  """
  image: String

  """
  Variant color attribute.
  """
  color: String

  """
  Default color for the product line.
  """
  default_color: String

  """
  Variant size attribute.
  """
  size: String

  """
  Variant status (e.g., 'active', 'draft', 'archived').
  """
  status: String

  """
  Whether the variant can be replenished/restocked.
  """
  replenishable: Boolean

  """
  Current stock quantity on hand.
  """
  stock: Int

  """
  Quantity on order from suppliers.
  """
  on_order: Int

  """
  Available-to-sell quantity (stock - committed).
  """
  ats: Int

  """
  Categories this variant belongs to.
  """
  categories: [VariantCategory]

  """
  Collections this variant is part of.
  """
  collections: [VariantCollection]

  """
  Tags associated with this variant.
  """
  tags: [VariantTag]

  """
  Vendors/suppliers for this variant.
  """
  vendors: [VariantVendor]

  """
  Timestamp when the variant was created in the source system.
  """
  source_created_at: String

  """
  Cost per item for margin calculations (in base currency).
  """
  cost_per_item: Float

  """
  Number of days the variant has been available on the site.
  """
  days_on_site: Int

  """
  Total gross sales revenue before returns and discounts.
  """
  gross_sales: Float

  """
  Total quantity sold (gross, before returns).
  """
  gross_qty: Int

  """
  Net sales after returns (gross_sales - return_value).
  """
  net_sales: Float

  """
  Net quantity after returns (gross_qty - return_qty).
  """
  net_qty: Int

  """
  Total sales including taxes, shipping, etc.
  """
  total_sales: Float

  """
  Total value of returned items.
  """
  return_value: Float

  """
  Total quantity of returned items.
  """
  return_qty: Int

  """
  Total discount amount applied.
  """
  discount: Float

  """
  Total tax amount collected.
  """
  tax: Float

  """
  Total shipping charges.
  """
  shipping: Float

  """
  Average sales per day over the analysis period.
  """
  sales_per_day: Float

  """
  Estimated days of sales remaining based on current sales velocity.
  """
  sales_days_left: Float

  """
  Sell-through rate as a percentage.
  """
  sell_through: Float

  """
  Gross profit (net_sales - cost).
  """
  gross_profit: Float

  """
  Gross margin percentage.
  """
  gross_margin: Float

  """
  Return rate as a percentage (return_qty / gross_qty * 100).
  """
  return_rate: Float

  """
  Total inventory value based on current stock.
  """
  total_inventory_value_stock: Float

  """
  Total inventory value based on available-to-sell quantity.
  """
  total_inventory_value_ats: Float

  """
  Weeks of supply remaining.
  """
  wos: Float

  """
  Recommended reorder quantity.
  """
  re_order_qty: Int

  """
  Percentage of total stock this variant represents.
  """
  stock_percentage: Float

  """
  Reorder point - minimum stock level before reordering.
  """
  rop: Float

  """
  Lead time in days for restocking.
  """
  lead_time: Int

  """
  Days of stock remaining at current sales velocity.
  """
  days_of_stock: Int

  """
  Percentage of total gross sales this variant represents.
  """
  gross_sales_percentage: Float

  """
  Total advertising spend allocated to this variant.
  """
  ad_spends: Float

  """
  Number of ad clicks.
  """
  clicks: Int

  """
  Number of product page views.
  """
  views: Int

  """
  Conversion rate (purchases / views).
  """
  conversion_rate: Float

  """
  Sales trend indicator (e.g., 'increasing', 'decreasing', 'stable').
  """
  trend: String

  """
  Sales pattern classification (e.g., 'seasonal', 'steady', 'volatile').
  """
  sale_pattern: String

  """
  Confidence score for forecasting accuracy.
  """
  confidence_score: String

  """
  Forecasted sales value.
  """
  forecast_value: Int

  """
  Forecasted sales per day.
  """
  forecast_sales_per_day: Int

  """
  Forecasted gross quantity to be sold.
  """
  forecasted_gross_qty: Int

  """
  Forecasted gross sales revenue.
  """
  forecasted_gross_sales: Float

  """
  Forecasted days of sales remaining based on current stock.
  """
  forecasted_sales_days_remaining_based_on_stock: Int

  """
  Forecasted days of sales remaining based on available-to-sell inventory.
  """
  forecasted_sales_days_remaining_based_on_ats: Int

  """
  Revenue grade classification (A/B/C analysis).
  """
  product_grade: RevenueGrade

  """
  Position of this variant within its collection.
  """
  collection_position: Int
}

"""
Comprehensive filter input for sales-by-variant queries.
Supports filtering by variant attributes, sales metrics, inventory data, and forecasting fields.
"""
input SalesByVariantFilter {
  """
  Filter by variant hash key.
  """
  h_key: StringFilterInput

  """
  Filter by source system.
  """
  from_source: StringFilterInput

  """
  Filter by variant ID.
  """
  variant_id: StringFilterInput

  """
  Filter by product ID.
  """
  product_id: StringFilterInput

  """
  Filter by product hash key.
  """
  product_h_key: StringFilterInput

  """
  Filter by variant name.
  """
  name: StringFilterInput

  """
  Filter by SKU.
  """
  sku: StringFilterInput

  """
  Filter by barcode.
  """
  barcode: StringFilterInput

  """
  Filter by color.
  """
  color: StringFilterInput

  """
  Filter by size.
  """
  size: StringFilterInput

  """
  Filter by status.
  """
  status: StringFilterInput

  """
  Filter by replenishable flag.
  """
  replenishable: BooleanFilterInput

  """
  Filter by stock quantity.
  """
  stock: NumericFilterInput

  """
  Filter by on-order quantity.
  """
  on_order: NumericFilterInput

  """
  Filter by available-to-sell quantity.
  """
  ats: NumericFilterInput

  """
  Filter by days on site.
  """
  days_on_site: NumericFilterInput

  """
  Filter by gross sales.
  """
  gross_sales: NumericFilterInput

  """
  Filter by gross quantity.
  """
  gross_qty: NumericFilterInput

  """
  Filter by net sales.
  """
  net_sales: NumericFilterInput

  """
  Filter by net quantity.
  """
  net_qty: NumericFilterInput

  """
  Filter by total sales.
  """
  total_sales: NumericFilterInput

  """
  Filter by return value.
  """
  return_value: NumericFilterInput

  """
  Filter by return quantity.
  """
  return_qty: NumericFilterInput

  """
  Filter by discount amount.
  """
  discount: NumericFilterInput

  """
  Filter by tax amount.
  """
  tax: NumericFilterInput

  """
  Filter by shipping amount.
  """
  shipping: NumericFilterInput

  """
  Filter by sales per day.
  """
  sales_per_day: NumericFilterInput

  """
  Filter by return rate.
  """
  return_rate: NumericFilterInput

  """
  Filter by price.
  """
  price: NumericFilterInput

  """
  Filter by compare-at price.
  """
  compare_at_price: NumericFilterInput

  """
  Filter by cost per item.
  """
  cost_per_item: NumericFilterInput

  """
  Filter by category hash keys.
  """
  categories_h_key: StringFilterInput

  """
  Filter by collection hash keys.
  """
  collections_h_key: StringFilterInput

  """
  Filter by category names.
  """
  categories_name: StringFilterInput

  """
  Filter by collection names.
  """
  collections_name: StringFilterInput

  """
  Filter by sales trend.
  """
  trend: StringFilterInput

  """
  Filter by sales pattern.
  """
  sale_pattern: StringFilterInput

  """
  Filter by forecast value.
  """
  forecast_value: NumericFilterInput

  """
  Filter by forecast sales per day.
  """
  forecast_sales_per_day: NumericFilterInput

  """
  Filter by forecasted gross quantity.
  """
  forecasted_gross_qty: NumericFilterInput

  """
  Filter by forecasted gross sales.
  """
  forecasted_gross_sales: NumericFilterInput

  """
  Filter by forecasted sales days remaining (stock-based).
  """
  forecasted_sales_days_remaining_based_on_stock: NumericFilterInput

  """
  Filter by forecasted sales days remaining (ATS-based).
  """
  forecasted_sales_days_remaining_based_on_ats: NumericFilterInput

  """
  Filter by stock percentage.
  """
  stock_percentage: NumericFilterInput

  """
  Filter by gross sales percentage.
  """
  gross_sales_percentage: NumericFilterInput

  """
  Filter by product grade (A/B/C).
  """
  product_grade: EnumFilterInput

  """
  Filter by total inventory value stock.
  """
  total_inventory_value_stock: NumericFilterInput

  """
  Filter by total inventory value ats.
  """
  total_inventory_value_ats: NumericFilterInput
}

"""
Input parameters for querying sales-by-variant analytics with comprehensive filtering and date range options.
"""
input SalesByVariantQueryInput {
  """
  Number of records to skip for pagination (0-based).
  """
  offset: Int

  """
  Maximum number of records to return (default: 50, max: 1000).
  """
  limit: Int

  """
  Start date for the analysis period (ISO 8601 format).
  """
  from_date: String

  """
  End date for the analysis period (ISO 8601 format).
  """
  to_date: String

  """
  Start date for forecast analysis (ISO 8601 format).
  """
  forecast_from_date: String

  """
  End date for forecast analysis (ISO 8601 format).
  """
  forecast_to_date: String

  """
  Sort criteria. Multiple sorts are applied in order.
  """
  sort: [SortInput]

  """
  Text search across variant names, SKUs, and other searchable fields.
  """
  search: String

  """
  Filter criteria to narrow down results.
  """
  filter: SalesByVariantFilter

  """
  Channel ID to filter results by specific sales channel.
  """
  channel_id: String
}

"""
Product variant entity representing specific variations of a product.
Variants are the actual sellable items with unique SKUs, prices, and attributes.
Each variant belongs to a parent product and has specific characteristics like color, size, etc.
"""
type Variant {
  """
  Unique hash key identifier for the variant.
  """
  h_key: String!

  """
  Source system identifier (e.g., 'shopify', 'magento', 'big-commerce',...).
  """
  from_source: String

  """
  Hash key of the parent product this variant belongs to.
  """
  parent_h_key: String

  """
  Business key identifier, typically from the source system.
  """
  b_key: String

  """
  Variant identifier from the source system.
  """
  variant_id: String

  """
  Product identifier from the source system.
  """
  product_id: String

  """
  Variant type classification ('simple').
  """
  type: String

  """
  Variant name displayed to customers.
  """
  name: String

  """
  Stock Keeping Unit - unique identifier for inventory tracking.
  """
  sku: String

  """
  Variant barcode for scanning and identification.
  """
  barcode: String

  """
  Cost per item for margin calculations (in base currency).
  """
  cost_per_item: Float

  """
  Current selling price (in base currency).
  """
  price: Float

  """
  Original price before discounts (in base currency).
  """
  compare_at_price: Float

  """
  URL or path to the variant-specific image.
  """
  image: String

  """
  Variant color attribute.
  """
  color: String

  """
  Default color for the product line.
  """
  default_color: String

  """
  Variant size attribute.
  """
  size: String

  """
  Variant status (e.g., 'active', 'draft', 'archived', 'deleted').
  """
  status: String

  """
  Timestamp when the variant was published/made available.
  """
  published_at: String

  """
  Categories this variant belongs to.
  """
  categories: [VariantCategory]

  """
  Collections this variant is part of.
  """
  collections: [VariantCollection]

  """
  Tags associated with this variant.
  """
  tags: [VariantTag]

  """
  Vendors/suppliers for this variant.
  """
  vendors: [VariantVendor]
}

"""
Filter input for querying variants with various criteria.
Supports filtering by variant attributes, pricing, and associated entities.
"""
input VariantsFilter {
  """
  Filter by variant hash key.
  """
  h_key: StringFilterInput

  """
  Filter by business key.
  """
  b_key: StringFilterInput

  """
  Filter by source system.
  """
  from_source: StringFilterInput

  """
  Filter by variant ID from source system.
  """
  variant_id: StringFilterInput

  """
  Filter by product ID from source system.
  """
  product_id: StringFilterInput

  """
  Filter by variant name (supports text search).
  """
  name: StringFilterInput

  """
  Filter by SKU.
  """
  sku: StringFilterInput

  """
  Filter by barcode.
  """
  barcode: StringFilterInput

  """
  Filter by color attribute.
  """
  color: StringFilterInput

  """
  Filter by size attribute.
  """
  size: StringFilterInput

  """
  Filter by variant status.
  """
  status: StringFilterInput

  """
  Filter by current selling price.
  """
  price: NumericFilterInput

  """
  Filter by compare-at price.
  """
  compare_at_price: NumericFilterInput

  """
  Filter by cost per item.
  """
  cost_per_item: NumericFilterInput

  """
  Filter by associated category hash keys.
  """
  categories_h_key: StringFilterInput

  """
  Filter by associated collection hash keys.
  """
  collections_h_key: StringFilterInput
}

"""
Input parameters for querying variants with pagination, sorting, and filtering.
"""
input VariantsQueryInput {
  """
  Number of records to skip for pagination (0-based).
  """
  offset: Int

  """
  Maximum number of records to return (default: 50, max: 1000).
  """
  limit: Int

  """
  Sort criteria. Multiple sorts are applied in order.
  """
  sort: [SortInput]

  """
  Filter criteria to narrow down results.
  """
  filter: VariantsFilter

  """
  Semantic search input.
  """
  semantic_search: SemanticSearchInput
}

"""
Response type for variant queries containing results and metadata.
"""
type VariantsQueryOutput {
  """
  Total number of variants matching the filter criteria.
  """
  total_count: Int

  """
  Array of variant records for the current page.
  """
  items: [Variant]
}
